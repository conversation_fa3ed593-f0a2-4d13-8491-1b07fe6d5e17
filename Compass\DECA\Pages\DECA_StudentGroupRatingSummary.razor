﻿@page "/deca-student-group-rating-summary"
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Web
@using Compass.DECA.DTOs

@attribute [Authorize]
@rendermode @(new InteractiveServerRenderMode(prerender: false))

<h1 class="deca-rating-summary-title">Student Group Rating Summary</h1>

<div class="roster-header-section">
    <h2 class="class-roster-title">Class Roster</h2>

    <div class="search-container">
        <input type="search" class="search-input" placeholder="Search" @bind="searchText" @bind:event="oninput" />
    </div>
</div>

@if (isLoading)
{
    <div class="loading-wrapper">
        <div class="spinner-border" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>
}
else if (studentRatings != null && studentRatings.Any())
{
    <div class="class-roster">
        @foreach (IGrouping<string, StudentGroupRatingDto> studentGroup in GetGroupedStudentRatings())
        {
            <div class="roster-row">
                <div class="student-header" @onclick="() => ToggleStudent(studentGroup.Key)" tabindex="0" role="button" aria-expanded="@(expandedStudents.Contains(studentGroup.Key) ? "true" : "false")" aria-label="Toggle details for @studentGroup.Key">
                    <div class="student-info">
                        <span class="student-name">@studentGroup.Key</span>
                    </div>
                    <div class="student-summary">
                        <span class="rating-date">@GetLatestRatingDate(studentGroup)</span>
                        <span class="rating-type">DECA-P2</span>
                        <span class="rater-type">Parent</span>
                        <span class="period">Other</span>
                        <button class="view-child-btn">
                            <i class="bi bi-eye"></i>
                            View Child
                        </button>
                    </div>
                </div>
                
                <section class="rating-details @(expandedStudents.Contains(studentGroup.Key) ? "expanded" : "")" aria-label="Student rating details">
                    <header class="ratings-grid-header">
                        <div class="header-cell">Rating Date</div>
                        <div class="header-cell">Rating Period</div>
                        <div class="header-cell">Rating Type</div>
                        <div class="header-cell">Teacher or Parent</div>
                        <div class="header-group">
                            <h3 class="header-group-title">Raw Score</h3>

                            <div class="header-subgroup">
                                <div class="header-subcell">IN</div>
                                <div class="header-subcell">SR</div>
                                <div class="header-subcell">AT</div>
                                <div class="header-subcell">TPF</div>
                                <div class="header-subcell">BC</div>
                            </div>
                        </div>
                        <div class="header-group">
                            <h3 class="header-group-title">T-Score</h3>

                            <div class="header-subgroup">
                                <div class="header-subcell">IN</div>
                                <div class="header-subcell">SR</div>
                                <div class="header-subcell">AT</div>
                                <div class="header-subcell">TPF</div>
                                <div class="header-subcell">BC</div>
                            </div>
                        </div>
                        <div class="header-group">
                            <h3 class="header-group-title">Percentile</h3>

                            <div class="header-subgroup">
                                <div class="header-subcell">IN</div>
                                <div class="header-subcell">SR</div>
                                <div class="header-subcell">AT</div>
                                <div class="header-subcell">TPF</div>
                                <div class="header-subcell">BC</div>
                            </div>
                        </div>
                        <div class="header-group">
                            <h3 class="header-group-title">Description</h3>

                            <div class="header-subgroup">
                                <div class="header-subcell">IN</div>
                                <div class="header-subcell">SR</div>
                                <div class="header-subcell">AT</div>
                                <div class="header-subcell">TPF</div>
                                <div class="header-subcell">BC</div>
                            </div>
                        </div>
                        <div class="header-cell">Actions</div>
                    </header>
                    <div class="ratings-grid-content">
                        @foreach (StudentGroupRatingDto rating in studentGroup)
                        {
                            <div class="rating-data-row">
                                <div class="data-cell">@GetFormattedDate(rating.RatingDate)</div>
                                <div class="data-cell">@GetSafeString(rating.RatingPeriod)</div>
                                <div class="data-cell">@GetSafeString(rating.RecordForm)</div>
                                <div class="data-cell">@GetSafeString(rating.RaterType)</div>
                                <div class="data-group">
                                    <div class="data-subcell">@GetSafeNumber(rating.InRaw)</div>
                                    <div class="data-subcell">@GetSafeNumber(rating.ScRaw)</div>
                                    <div class="data-subcell">@GetSafeNumber(rating.ArRaw)</div>
                                    <div class="data-subcell">@GetSafeNumber(rating.TpfRaw)</div>
                                    <div class="data-subcell">@GetSafeNumber(rating.BcRaw)</div>
                                </div>
                                <div class="data-group">
                                    <div class="data-subcell">@GetSafeNumber(rating.InTScore)</div>
                                    <div class="data-subcell">@GetSafeNumber(rating.ScTScore)</div>
                                    <div class="data-subcell">@GetSafeNumber(rating.ArTScore)</div>
                                    <div class="data-subcell">@GetSafeNumber(rating.TpfTScore)</div>
                                    <div class="data-subcell">@GetSafeNumber(rating.BcTScore)</div>
                                </div>
                                <div class="data-group">
                                    <div class="data-subcell">@GetSafeNumber(rating.InPercentile)</div>
                                    <div class="data-subcell">@GetSafeNumber(rating.ScPercentile)</div>
                                    <div class="data-subcell">@GetSafeNumber(rating.ArPercentile)</div>
                                    <div class="data-subcell">@GetSafeNumber(rating.TpfPercentile)</div>
                                    <div class="data-subcell">@GetSafeNumber(rating.BcPercentile)</div>
                                </div>
                                <div class="data-group">
                                    <div class="data-subcell">@GetSafeString(rating.InDescription)</div>
                                    <div class="data-subcell">@GetSafeString(rating.ScDescription)</div>
                                    <div class="data-subcell">@GetSafeString(rating.AtDescription)</div>
                                    <div class="data-subcell">@GetSafeString(rating.TpfDescription)</div>
                                    <div class="data-subcell">@GetSafeString(rating.BcDescription)</div>
                                </div>
                                <div class="data-cell">
                                    <div class="rating-actions">
                                        <button class="action-btn" type="button" title="Edit rating" aria-label="Edit rating for @GetStudentName(rating.FirstName, rating.LastName)">
                                            <i class="bi bi-pencil" aria-hidden="true"></i>
                                        </button>
                                        <button class="action-btn" type="button" title="Refresh rating" aria-label="Refresh rating for @GetStudentName(rating.FirstName, rating.LastName)">
                                            <i class="bi bi-arrow-clockwise" aria-hidden="true"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        }
                    </div>
                </section>
            </div>
        }
    </div>
}
else
{
    <div class="alert alert-info no-data-message">
        <i class="bi bi-info-circle" aria-hidden="true"></i>
        <p>No student ratings found for this student group.</p>
    </div>
}

@code {
    private string searchText = "";
    private HashSet<string> expandedStudents = new();

    private IEnumerable<IGrouping<string, StudentGroupRatingDto>> GetGroupedStudentRatings()
    {
        if (studentRatings == null) return Enumerable.Empty<IGrouping<string, StudentGroupRatingDto>>();

        var filtered = string.IsNullOrWhiteSpace(searchText)
            ? studentRatings
            : studentRatings.Where(r =>
                (r.FirstName + " " + r.LastName).Contains(searchText, StringComparison.OrdinalIgnoreCase));

        return filtered.GroupBy(r => GetStudentName(r.FirstName, r.LastName));
    }

    private void ToggleStudent(string studentName)
    {
        if (expandedStudents.Contains(studentName))
            expandedStudents.Remove(studentName);
        else
            expandedStudents.Add(studentName);
    }

    private string GetLatestRatingDate(IEnumerable<StudentGroupRatingDto> ratings)
    {
        if (ratings == null || !ratings.Any())
        {
            return "";
        }

        var validDates = ratings.Where(r => r?.RatingDate.HasValue == true).Select(r => r.RatingDate!.Value);
        if (!validDates.Any())
        {
            return "";
        }

        var latestDate = validDates.Max();
        return GetFormattedDate(latestDate);
    }

    private string GetStudentName(string? firstName, string? lastName)
    {
        var first = GetSafeString(firstName);
        var last = GetSafeString(lastName);

        if (string.IsNullOrWhiteSpace(first) && string.IsNullOrWhiteSpace(last))
        {
            return "Unknown Student";
        }

        return $"{first} {last}".Trim();
    }

    private string GetFormattedDate(DateTime? date)
    {
        return date?.ToString("MM/dd/yyyy") ?? "";
    }

    private string GetSafeString(string? value)
    {
        return value ?? "";
    }

    private string GetSafeNumber(int? value)
    {
        return value?.ToString() ?? "";
    }
}
