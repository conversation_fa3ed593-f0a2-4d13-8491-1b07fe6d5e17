using Compass.Common.Data;
using Compass.Common.SessionHandlers;
using Compass.Common.Services;
using Compass.DECA.DTOs;
using Compass.DECA.Interfaces.Services;
using Compass.DECA.Services;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Compass.Common.DTOs.Generic;
using Compass.Common.DTOs.User;
using Compass.Common.Helpers;
using Compass.Common.Models;

namespace Compass.DECA.Pages
{
    public partial class DECA_StudentGroupRatingSummary : IDisposable
    {
        [Inject]
        public required NavigationManager NavigationManager { get; set; }

        [Inject]
        public required UserAccessor UserAccessor { get; set; }

        [Inject]
        public required UserSessionService UserSessionService { get; set; }

        [Inject]
        public required CommonSessionDataObserver CommonSessionDataObserver { get; set; }

        [Inject]
        public required IDecaStudentRatingService DecaStudentRatingService { get; set; }

        private bool isLoading = true;
        private List<StudentGroupRatingDto>? studentRatings;

        private string? _currentUserId;
        private ApplicationUser? _currentUser;
        private long? currentOrganizationId;
        private long? currentStudentGroupId;

        private async Task<CommonSessionData?> GetCommonSessionData()
        {
            CommonSessionData? commonSessionData = null;

            var (user, userId) = await UserAccessor.GetUserAndIdAsync();
            _currentUser = user;
            _currentUserId = userId;
            if (_currentUser != null)
            {
                commonSessionData = await UserSessionService.GetUserSessionAsync(_currentUser.Id);
            }

            return commonSessionData;
        }
        protected override async Task OnInitializedAsync()
        {

            CommonSessionData? commonSessionData = await GetCommonSessionData();
            if (commonSessionData != null)
            {
                currentOrganizationId = commonSessionData.CurrentOrganizationId;
                currentStudentGroupId = commonSessionData.CurrentStudentGroupId;

                await GetStudentRatingData();
            }
        }

        private async Task GetStudentRatingData()
        {
            try
            {
                isLoading = true;
                StateHasChanged();

                if (currentStudentGroupId.HasValue)
                {
                    var ratings = await DecaStudentRatingService.GetCurrentStudentRatingsForStudentGroupAsync(currentStudentGroupId.Value);

                    // Sort by student name first, then by rating date descending
                    studentRatings = ratings
                        .OrderBy(r => r.LastName)
                        .ThenBy(r => r.FirstName)
                        .ThenByDescending(r => r.RatingDate)
                        .ToList();
                }
            }
            catch (Exception ex)
            {
                // Log error or handle appropriately
                Console.WriteLine($"Error loading DECA student ratings: {ex.Message}");
            }
            finally
            {
                isLoading = false;
                StateHasChanged();
            }
        }

        private async Task NavigateToEditRating(long studentId, long? ratingId = null)
        {
            await SetStudentContext(studentId);

            if (ratingId.HasValue)
            {
                DECASessionData.RatingId = ratingId.Value;
            }
            else
            {
                DECASessionData.RatingId = null;
            }

            NavigationManager.NavigateTo("/deca-student-rating");
        }

        private async Task NavigateToStudentRatings(long studentId)
        {
            await SetStudentContext(studentId);

            NavigationManager.NavigateTo("/deca-student-ratings");
        }

        private async Task SetStudentContext(long studentId)
        {
            try
            {
                var commonSessionData = await GetCommonSessionData();
                if (commonSessionData != null && _currentUser != null)
                {

                    commonSessionData.CurrentStudentId = studentId;

                    var student = studentRatings?.FirstOrDefault(r => r.StudentId == studentId);
                    if (student != null)
                    {
                        string studentName = $"{student.FirstName} {student.LastName}".Trim();
                        commonSessionData.SelectedEntityName = studentName;
                    }

                    await UserSessionService.SetUserSessionAsync(_currentUser.Id, commonSessionData);
                    await CommonSessionDataObserver.BroadcastStateChangeAsync();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error setting student context: {ex.Message}");
            }
        }

        public void Dispose()
        {
            // Clean up any resources if needed
            GC.SuppressFinalize(this);
        }
    }
}
